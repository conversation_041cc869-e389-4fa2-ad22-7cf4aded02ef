{"$schema": "https://schema.tauri.app/config/2", "productName": "mein-overlay", "version": "0.1.0", "identifier": "com.mein-overlay.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "MeinWort Overlay", "width": 200, "height": 200, "resizable": true, "decorations": false, "alwaysOnTop": true, "skipTaskbar": true, "transparent": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}