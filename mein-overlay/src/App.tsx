import { useState } from "react";
import { getCurrentWindow } from "@tauri-apps/api/window";
import { Background } from "./components/Background";
import { CompactView } from "./components/CompactView";
import { ExpandedView } from "./components/ExpandedView";

function App() {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activePanel, setActivePanel] = useState<string | null>(null);

  const handleButtonClick = async (buttonType: string) => {
    const appWindow = getCurrentWindow();

    if (!isExpanded) {
      // Fenster auf 400x600 erweitern
      await appWindow.setSize({ width: 400, height: 600 });
      setIsExpanded(true);
    }

    setActivePanel(buttonType);
  };

  const handleClose = async () => {
    const appWindow = getCurrentWindow();

    // Fenster zurück auf 200x200 verkleinern
    await appWindow.setSize({ width: 200, height: 200 });
    setIsExpanded(false);
    setActivePanel(null);
  };

  return (
    <Background isExpanded={isExpanded}>
      {/* Kompakte Ansicht - 200x200 */}
      {!isExpanded && (
        <CompactView onButtonClick={handleButtonClick} />
      )}

      {/* Erweiterte Ansicht - 400x600 */}
      {isExpanded && activePanel && (
        <ExpandedView
          activePanel={activePanel}
          onClose={handleClose}
        />
      )}
    </Background>
  );
}

export default App;