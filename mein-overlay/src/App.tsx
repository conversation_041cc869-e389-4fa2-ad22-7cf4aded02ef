import { useState } from "react";
import { getCurrentWindow } from "@tauri-apps/api/window";

function App() {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activePanel, setActivePanel] = useState<string | null>(null);

  const handleButtonClick = async (buttonType: string) => {
    const appWindow = getCurrentWindow();
    
    if (!isExpanded) {
      // Expand window to 400x600
      await appWindow.setSize({ width: 400, height: 600 });
      setIsExpanded(true);
    }
    
    setActivePanel(buttonType);
  };

  const handleClose = async () => {
    const appWindow = getCurrentWindow();
    
    // Collapse window back to 200x200
    await appWindow.setSize({ width: 200, height: 200 });
    setIsExpanded(false);
    setActivePanel(null);
  };

  return (
    <div 
      className="w-full h-full relative overflow-hidden"
      data-tauri-drag-region
      style={{
        background: `
          linear-gradient(135deg, rgba(0,0,0,0.9) 0%, rgba(20,20,20,0.95) 100%),
          url("data:image/svg+xml,${encodeURIComponent(`
            <svg width="400" height="600" viewBox="0 0 400 600" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <linearGradient id="wave" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:0.3" />
                  <stop offset="50%" style="stop-color:#f59e0b;stop-opacity:0.5" />
                  <stop offset="100%" style="stop-color:#d97706;stop-opacity:0.3" />
                </linearGradient>
              </defs>
              <path d="M0,300 Q100,200 200,300 T400,300 L400,600 L0,600 Z" fill="url(#wave)"/>
              <path d="M0,350 Q120,250 240,350 T480,350 L480,600 L0,600 Z" fill="url(#wave)" opacity="0.7"/>
              <path d="M0,400 Q80,320 160,400 T320,400 L320,600 L0,600 Z" fill="url(#wave)" opacity="0.5"/>
            </svg>
          `)}")`
        }}
    >
      {/* Compact View - 200x200 */}
      {!isExpanded && (
        <div className="h-full flex flex-col justify-end p-4">
          <div className="grid grid-cols-3 gap-3">
            <button
              onClick={() => handleButtonClick('document')}
              className="bg-black/60 hover:bg-black/70 text-white rounded-xl p-2 flex flex-col items-center justify-center backdrop-blur-sm border border-white/20 transition-all duration-200 hover:scale-105 shadow-lg"
            >
              <div className="text-white text-base mb-0.5">📄</div>
              <span className="text-[10px] font-semibold tracking-tight">DOCUMENT</span>
            </button>
            
            <button
              onClick={() => handleButtonClick('ai')}
              className="bg-orange-700/90 hover:bg-orange-600/90 text-white rounded-xl p-2 flex flex-col items-center justify-center backdrop-blur-sm border border-orange-500/30 transition-all duration-200 hover:scale-105 shadow-lg"
            >
              <div className="text-white text-base mb-0.5">⭐</div>
              <span className="text-[10px] font-semibold tracking-tight">AI+</span>
            </button>
            
            <button
              onClick={() => handleButtonClick('select')}
              className="bg-black/60 hover:bg-black/70 text-white rounded-xl p-2 flex flex-col items-center justify-center backdrop-blur-sm border border-white/20 transition-all duration-200 hover:scale-105 shadow-lg"
            >
              <div className="text-white text-base mb-0.5">⚪</div>
              <span className="text-[10px] font-semibold tracking-tight">SELECT+</span>
            </button>
          </div>
        </div>
      )}

      {/* Expanded View - 400x600 */}
      {isExpanded && (
        <div className="h-full flex flex-col">
          {/* Header with Close Button */}
          <div className="flex justify-between items-center p-4 border-b border-white/10">
            <h2 className="text-white font-semibold">
              {activePanel === 'document' && 'Document Panel'}
              {activePanel === 'ai' && 'AI+ Panel'}
              {activePanel === 'select' && 'Select+ Panel'}
            </h2>
            <button
              onClick={handleClose}
              className="text-white/70 hover:text-white text-xl leading-none"
            >
              ×
            </button>
          </div>

          {/* Panel Content */}
          <div className="flex-1 p-4 text-white">
            {activePanel === 'document' && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Document Tools</h3>
                <div className="space-y-2">
                  <button className="w-full bg-white/10 hover:bg-white/20 p-3 rounded-lg text-left transition-colors">
                    📝 New Document
                  </button>
                  <button className="w-full bg-white/10 hover:bg-white/20 p-3 rounded-lg text-left transition-colors">
                    📂 Open Document
                  </button>
                  <button className="w-full bg-white/10 hover:bg-white/20 p-3 rounded-lg text-left transition-colors">
                    💾 Save Document
                  </button>
                </div>
              </div>
            )}

            {activePanel === 'ai' && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">AI Assistant</h3>
                <div className="space-y-2">
                  <button className="w-full bg-orange-600/20 hover:bg-orange-600/30 p-3 rounded-lg text-left transition-colors">
                    🤖 Start AI Chat
                  </button>
                  <button className="w-full bg-orange-600/20 hover:bg-orange-600/30 p-3 rounded-lg text-left transition-colors">
                    ✨ AI Suggestions
                  </button>
                  <button className="w-full bg-orange-600/20 hover:bg-orange-600/30 p-3 rounded-lg text-left transition-colors">
                    🔍 AI Analysis
                  </button>
                </div>
              </div>
            )}

            {activePanel === 'select' && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Selection Tools</h3>
                <div className="space-y-2">
                  <button className="w-full bg-white/10 hover:bg-white/20 p-3 rounded-lg text-left transition-colors">
                    🎯 Area Select
                  </button>
                  <button className="w-full bg-white/10 hover:bg-white/20 p-3 rounded-lg text-left transition-colors">
                    📱 Window Select
                  </button>
                  <button className="w-full bg-white/10 hover:bg-white/20 p-3 rounded-lg text-left transition-colors">
                    🖱️ Element Select
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default App;