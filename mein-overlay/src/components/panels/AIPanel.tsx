import React from 'react';

export const AIPanel: React.FC = () => {
  const aiActions = [
    { icon: '🤖', label: 'KI Chat starten', action: 'chat' },
    { icon: '✨', label: '<PERSON><PERSON>lä<PERSON>', action: 'suggestions' },
    { icon: '🔍', label: 'Text analysieren', action: 'analyze' },
    { icon: '📝', label: 'Text generieren', action: 'generate' },
    { icon: '🌐', label: 'Übersetzen', action: 'translate' },
    { icon: '📊', label: 'Z<PERSON>mmenfassen', action: 'summarize' }
  ];

  const handleAction = (action: string) => {
    console.log(`KI Aktion: ${action}`);
    // Hier würde die eigentliche KI-Funktionalität implementiert
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-xl font-semibold text-white mb-2">KI Assistent</h3>
        <p className="text-white/70 text-sm">Nutzen Sie die Kraft der künstlichen Intelligenz</p>
      </div>
      
      <div className="grid grid-cols-2 gap-3">
        {aiActions.map((item, index) => (
          <button
            key={index}
            onClick={() => handleAction(item.action)}
            className="bg-gradient-to-br from-orange-600/20 to-orange-700/20 hover:from-orange-500/30 hover:to-orange-600/30 backdrop-blur-sm p-4 rounded-xl text-left transition-all duration-200 hover:scale-105 border border-orange-400/30 hover:border-orange-300/50"
          >
            <div className="flex items-center space-x-3">
              <span className="text-2xl">{item.icon}</span>
              <span className="text-white font-medium text-sm">{item.label}</span>
            </div>
          </button>
        ))}
      </div>
      
      <div className="mt-6 p-4 bg-orange-600/10 rounded-xl border border-orange-400/20">
        <p className="text-orange-200 text-xs text-center">
          💡 Tipp: Markieren Sie Text für kontextbezogene KI-Hilfe
        </p>
      </div>
    </div>
  );
};
