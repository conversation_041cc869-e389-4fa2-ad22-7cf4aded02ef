import React from 'react';

export const SelectPanel: React.FC = () => {
  const selectActions = [
    { icon: '🎯', label: '<PERSON><PERSON>ich auswählen', action: 'area' },
    { icon: '📱', label: 'Fenster auswählen', action: 'window' },
    { icon: '🖱️', label: 'Element auswählen', action: 'element' },
    { icon: '📸', label: 'Screenshot', action: 'screenshot' },
    { icon: '🎥', label: 'Bildschirm aufnehmen', action: 'record' },
    { icon: '📋', label: 'Text extrahieren', action: 'extract' }
  ];

  const handleAction = (action: string) => {
    console.log(`Auswahl Aktion: ${action}`);
    // Hier würde die eigentliche Auswahl-Funktionalität implementiert
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-xl font-semibold text-white mb-2">Auswahl Tools</h3>
        <p className="text-white/70 text-sm">Präzise Auswahl und Erfassung von Bildschirminhalten</p>
      </div>
      
      <div className="grid grid-cols-2 gap-3">
        {selectActions.map((item, index) => (
          <button
            key={index}
            onClick={() => handleAction(item.action)}
            className="bg-white/10 hover:bg-white/20 backdrop-blur-sm p-4 rounded-xl text-left transition-all duration-200 hover:scale-105 border border-white/20 hover:border-white/40"
          >
            <div className="flex items-center space-x-3">
              <span className="text-2xl">{item.icon}</span>
              <span className="text-white font-medium text-sm">{item.label}</span>
            </div>
          </button>
        ))}
      </div>
      
      <div className="mt-6 p-4 bg-blue-600/10 rounded-xl border border-blue-400/20">
        <p className="text-blue-200 text-xs text-center">
          🔧 Tipp: Verwenden Sie Tastenkürzel für schnelle Auswahl
        </p>
      </div>
    </div>
  );
};
