import React from 'react';

export const DocumentPanel: React.FC = () => {
  const documentActions = [
    { icon: '📝', label: 'Neues Dokument', action: 'new' },
    { icon: '📂', label: 'Doku<PERSON> öffnen', action: 'open' },
    { icon: '💾', label: 'Dokument speichern', action: 'save' },
    { icon: '📋', label: 'Zwischenablage', action: 'clipboard' },
    { icon: '🔍', label: 'Text suchen', action: 'search' },
    { icon: '📄', label: 'PDF exportieren', action: 'pdf' }
  ];

  const handleAction = (action: string) => {
    console.log(`Dokument Aktion: ${action}`);
    // Hier würde die eigentliche Funktionalität implementiert
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-xl font-semibold text-white mb-2">Dokument Verwaltung</h3>
        <p className="text-white/70 text-sm">Verwalten Sie Ihre Dokumente effizient</p>
      </div>
      
      <div className="grid grid-cols-2 gap-3">
        {documentActions.map((item, index) => (
          <button
            key={index}
            onClick={() => handleAction(item.action)}
            className="bg-white/10 hover:bg-white/20 backdrop-blur-sm p-4 rounded-xl text-left transition-all duration-200 hover:scale-105 border border-white/20 hover:border-white/40"
          >
            <div className="flex items-center space-x-3">
              <span className="text-2xl">{item.icon}</span>
              <span className="text-white font-medium text-sm">{item.label}</span>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};
