import React from 'react';
import { DocumentPanel } from './panels/DocumentPanel';
import { AIPanel } from './panels/AIPanel';
import { SelectPanel } from './panels/SelectPanel';

interface ExpandedViewProps {
  activePanel: string;
  onClose: () => void;
}

export const ExpandedView: React.FC<ExpandedViewProps> = ({ activePanel, onClose }) => {
  const getPanelTitle = () => {
    switch (activePanel) {
      case 'document': return 'Dokument Tools';
      case 'ai': return 'KI Assistent';
      case 'select': return 'Auswahl Tools';
      default: return 'Panel';
    }
  };

  const renderPanel = () => {
    switch (activePanel) {
      case 'document': return <DocumentPanel />;
      case 'ai': return <AIPanel />;
      case 'select': return <SelectPanel />;
      default: return null;
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header mit Schl<PERSON>ßen Button */}
      <div className="flex justify-between items-center p-6 border-b border-white/20 backdrop-blur-sm">
        <h2 className="text-white font-bold text-lg tracking-wide">
          {getPanelTitle()}
        </h2>
        <button
          onClick={onClose}
          className="text-white/70 hover:text-white text-2xl leading-none hover:bg-white/10 rounded-full w-8 h-8 flex items-center justify-center transition-all duration-200"
        >
          ×
        </button>
      </div>

      {/* Panel Inhalt */}
      <div className="flex-1 p-6 overflow-y-auto">
        {renderPanel()}
      </div>
    </div>
  );
};
