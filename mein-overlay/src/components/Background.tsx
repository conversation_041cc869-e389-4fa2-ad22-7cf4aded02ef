import React from 'react';

interface BackgroundProps {
  children: React.ReactNode;
  isExpanded?: boolean;
}

export const Background: React.FC<BackgroundProps> = ({ children, isExpanded = false }) => {
  const wavePattern = `
    <svg width="400" height="600" viewBox="0 0 400 600" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="wave1" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:0.4" />
          <stop offset="50%" style="stop-color:#f59e0b;stop-opacity:0.6" />
          <stop offset="100%" style="stop-color:#d97706;stop-opacity:0.3" />
        </linearGradient>
        <linearGradient id="wave2" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:0.3" />
          <stop offset="50%" style="stop-color:#d97706;stop-opacity:0.5" />
          <stop offset="100%" style="stop-color:#b45309;stop-opacity:0.2" />
        </linearGradient>
        <linearGradient id="wave3" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:0.2" />
          <stop offset="50%" style="stop-color:#f59e0b;stop-opacity:0.4" />
          <stop offset="100%" style="stop-color:#d97706;stop-opacity:0.1" />
        </linearGradient>
      </defs>
      
      <!-- Mehrere Wellenebenen für mehr Tiefe -->
      <path d="M0,300 Q100,200 200,300 T400,300 L400,600 L0,600 Z" fill="url(#wave1)"/>
      <path d="M0,350 Q120,250 240,350 T480,350 L480,600 L0,600 Z" fill="url(#wave2)" opacity="0.8"/>
      <path d="M0,400 Q80,320 160,400 T320,400 L320,600 L0,600 Z" fill="url(#wave3)" opacity="0.6"/>
      <path d="M0,450 Q150,370 300,450 T600,450 L600,600 L0,600 Z" fill="url(#wave1)" opacity="0.4"/>
      
      <!-- Zusätzliche Glanzeffekte -->
      <circle cx="100" cy="250" r="2" fill="#fbbf24" opacity="0.6"/>
      <circle cx="300" cy="200" r="1.5" fill="#f59e0b" opacity="0.8"/>
      <circle cx="200" cy="180" r="1" fill="#fbbf24" opacity="0.7"/>
      <circle cx="350" cy="220" r="1.2" fill="#d97706" opacity="0.5"/>
    </svg>
  `;

  const backgroundStyle = {
    background: `
      radial-gradient(circle at 20% 20%, rgba(251, 191, 36, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(245, 158, 11, 0.1) 0%, transparent 50%),
      linear-gradient(135deg, rgba(0,0,0,0.95) 0%, rgba(15,15,15,0.98) 50%, rgba(0,0,0,0.95) 100%),
      url("data:image/svg+xml,${encodeURIComponent(wavePattern)}")
    `,
    backgroundSize: 'cover, cover, cover, cover',
    backgroundPosition: 'center, center, center, bottom',
    backgroundRepeat: 'no-repeat'
  };

  return (
    <div 
      className="w-full h-full relative overflow-hidden"
      data-tauri-drag-region
      style={backgroundStyle}
    >
      {/* Zusätzlicher Overlay für mehr Tiefe */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none" />
      
      {/* Inhalt */}
      <div className="relative z-10 h-full">
        {children}
      </div>
    </div>
  );
};
