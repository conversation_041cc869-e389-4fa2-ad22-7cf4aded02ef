import React from 'react';

interface ActionButtonProps {
  icon: string;
  label: string;
  onClick: () => void;
  variant?: 'default' | 'primary';
  className?: string;
}

export const ActionButton: React.FC<ActionButtonProps> = ({
  icon,
  label,
  onClick,
  variant = 'default',
  className = ''
}) => {
  const baseClasses = "rounded-2xl p-4 flex flex-col items-center justify-center backdrop-blur-sm transition-all duration-300 hover:scale-105 shadow-xl border";
  
  const variantClasses = {
    default: "bg-black/70 hover:bg-black/80 text-white border-white/30 hover:border-white/50",
    primary: "bg-gradient-to-br from-orange-600/90 to-orange-700/90 hover:from-orange-500/90 hover:to-orange-600/90 text-white border-orange-400/40 hover:border-orange-300/60"
  };

  return (
    <button
      onClick={onClick}
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
    >
      <div className="text-2xl mb-2 filter drop-shadow-lg">
        {icon}
      </div>
      <span className="text-xs font-bold tracking-wider uppercase">
        {label}
      </span>
    </button>
  );
};
