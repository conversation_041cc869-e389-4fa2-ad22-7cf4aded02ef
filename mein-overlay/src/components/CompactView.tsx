import React from 'react';
import { ActionButton } from './ActionButton';

interface CompactViewProps {
  onButtonClick: (buttonType: string) => void;
}

export const CompactView: React.FC<CompactViewProps> = ({ onButtonClick }) => {
  return (
    <div className="h-full flex flex-col justify-end p-6">
      <div className="grid grid-cols-3 gap-4">
        <ActionButton
          icon="📄"
          label="DOCUMENT"
          onClick={() => onButtonClick('document')}
          variant="default"
        />
        
        <ActionButton
          icon="⭐"
          label="AI+"
          onClick={() => onButtonClick('ai')}
          variant="primary"
        />
        
        <ActionButton
          icon="⚪"
          label="SELECT+"
          onClick={() => onButtonClick('select')}
          variant="default"
        />
      </div>
    </div>
  );
};
